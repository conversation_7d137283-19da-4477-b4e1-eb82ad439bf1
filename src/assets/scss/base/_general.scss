@use "./mixins" as *;

@view-transition {
  navigation: auto;
}

*:focus,
*:focus-visible {
  @include outline;
}

*:focus:not(:focus-visible) {
  outline: none;
  box-shadow: none;
}

// Body
html,
body {
  height: 100%;
}

body {
  border-top: 5px solid hsla(280, 85%, 55%, 1);
  min-height: 100vh;
  font-weight: 400;
  line-height: 1.65;
  font-family: var(--font-body);
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  text-shadow: rgba(0, 0, 0, 0.01) 0 0 1px;
  -webkit-text-size-adjust: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: flex;
  flex-direction: column;
  background-color: var(--background-color);
  overflow-x: clip;
  color: var(--foreground-color);

  main {
    flex: 1 0 auto;
  }

  footer {
    flex-shrink: 0;
  }
}

@media (min-width: 900px) {
  body {
    background-image: url("data:image/svg+xml,%3Csvg width='120' height='120' viewBox='0 0 120 120' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 0h2v20H9V0zm25.134.84l1.732 1-10 17.32-1.732-1 10-17.32zm-20 20l1.732 1-10 17.32-1.732-1 10-17.32zM58.16 4.134l1 1.732-17.32 10-1-1.732 17.32-10zm-40 40l1 1.732-17.32 10-1-1.732 17.32-10zM80 9v2H60V9h20zM20 69v2H0v-2h20zm79.32-55l-1 1.732-17.32-10L82 4l17.32 10zm-80 80l-1 1.732-17.32-10L2 84l17.32 10zm96.546-75.84l-1.732 1-10-17.32 1.732-1 10 17.32zm-100 100l-1.732 1-10-17.32 1.732-1 10 17.32zM38.16 24.134l1 1.732-17.32 10-1-1.732 17.32-10zM60 29v2H40v-2h20zm19.32 5l-1 1.732-17.32-10L62 24l17.32 10zm16.546 4.16l-1.732 1-10-17.32 1.732-1 10 17.32zM111 40h-2V20h2v20zm3.134.84l1.732 1-10 17.32-1.732-1 10-17.32zM40 49v2H20v-2h20zm19.32 5l-1 1.732-17.32-10L42 44l17.32 10zm16.546 4.16l-1.732 1-10-17.32 1.732-1 10 17.32zM91 60h-2V40h2v20zm3.134.84l1.732 1-10 17.32-1.732-1 10-17.32zm24.026 3.294l1 1.732-17.32 10-1-1.732 17.32-10zM39.32 74l-1 1.732-17.32-10L22 64l17.32 10zm16.546 4.16l-1.732 1-10-17.32 1.732-1 10 17.32zM71 80h-2V60h2v20zm3.134.84l1.732 1-10 17.32-1.732-1 10-17.32zm24.026 3.294l1 1.732-17.32 10-1-1.732 17.32-10zM120 89v2h-20v-2h20zm-84.134 9.16l-1.732 1-10-17.32 1.732-1 10 17.32zM51 100h-2V80h2v20zm3.134.84l1.732 1-10 17.32-1.732-1 10-17.32zm24.026 3.294l1 1.732-17.32 10-1-1.732 17.32-10zM100 109v2H80v-2h20zm19.32 5l-1 1.732-17.32-10 1-1.732 17.32 10zM31 120h-2v-20h2v20z' fill='%239C92AC' fill-opacity='0.4' fill-rule='evenodd'/%3E%3C/svg%3E");
    background-repeat: repeat-y;
  }

  .darkmode {
    body {
      background-image: url("data:image/svg+xml,%3Csvg width='120' height='120' viewBox='0 0 120 120' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 0h2v20H9V0zm25.134.84l1.732 1-10 17.32-1.732-1 10-17.32zm-20 20l1.732 1-10 17.32-1.732-1 10-17.32zM58.16 4.134l1 1.732-17.32 10-1-1.732 17.32-10zm-40 40l1 1.732-17.32 10-1-1.732 17.32-10zM80 9v2H60V9h20zM20 69v2H0v-2h20zm79.32-55l-1 1.732-17.32-10L82 4l17.32 10zm-80 80l-1 1.732-17.32-10L2 84l17.32 10zm96.546-75.84l-1.732 1-10-17.32 1.732-1 10 17.32zm-100 100l-1.732 1-10-17.32 1.732-1 10 17.32zM38.16 24.134l1 1.732-17.32 10-1-1.732 17.32-10zM60 29v2H40v-2h20zm19.32 5l-1 1.732-17.32-10L62 24l17.32 10zm16.546 4.16l-1.732 1-10-17.32 1.732-1 10 17.32zM111 40h-2V20h2v20zm3.134.84l1.732 1-10 17.32-1.732-1 10-17.32zM40 49v2H20v-2h20zm19.32 5l-1 1.732-17.32-10L42 44l17.32 10zm16.546 4.16l-1.732 1-10-17.32 1.732-1 10 17.32zM91 60h-2V40h2v20zm3.134.84l1.732 1-10 17.32-1.732-1 10-17.32zm24.026 3.294l1 1.732-17.32 10-1-1.732 17.32-10zM39.32 74l-1 1.732-17.32-10L22 64l17.32 10zm16.546 4.16l-1.732 1-10-17.32 1.732-1 10 17.32zM71 80h-2V60h2v20zm3.134.84l1.732 1-10 17.32-1.732-1 10-17.32zm24.026 3.294l1 1.732-17.32 10-1-1.732 17.32-10zM120 89v2h-20v-2h20zm-84.134 9.16l-1.732 1-10-17.32 1.732-1 10 17.32zM51 100h-2V80h2v20zm3.134.84l1.732 1-10 17.32-1.732-1 10-17.32zm24.026 3.294l1 1.732-17.32 10-1-1.732 17.32-10zM100 109v2H80v-2h20zm19.32 5l-1 1.732-17.32-10 1-1.732 17.32 10zM31 120h-2v-20h2v20z' fill='%23936dd1' fill-opacity='0.6' fill-rule='evenodd'/%3E%3C/svg%3E");
      background-repeat: repeat-y;
    }
  }
}

// Links
a {
  transition: box-shadow 0.2s;
  box-shadow: inset 0 -2px 0 var(--brand-primary, #ed1b2e);
  background: 0 0;
  padding: 1px 0 0 0;
  color: inherit;
  text-decoration: none;
}

a:hover,
a:focus,
a:active {
  box-shadow: inset 0 -1.3em 0 var(--brand-background, #f3f7f9);
}

// Blockquotes
blockquote {
  margin-top: 0;
  margin-right: 1.75rem;
  margin-bottom: 1.75rem;
  margin-left: -1.75rem;
  border-left: 0.3rem solid hsla(0, 0%, 0%, 0.9);
  padding-top: 0;
  padding-right: 0;
  padding-bottom: 0;
  padding-left: 1.4rem;
  color: hsla(0, 0%, 0%, 0.59);
  font-style: italic;
  font-size: 1.2rem;
  line-height: 1.75rem;
}

blockquote > p {
  font-size: 1.5em;
  line-height: 1.3em;
}

@media (min-width: 600px) {
  blockquote {
    margin-top: 3rem;
    margin-bottom: 3rem;
  }
}

@media (min-width: 1100px) {
  blockquote {
    position: relative;
    border-color: transparent;
  }

  blockquote::after {
    position: absolute;
    top: 0.15em;
    left: -0.3em;
    content: "“";
    color: hsla(280, 85%, 55%, 0.7);
    font-size: 5em;
    font-family: "Helvetica Neue", Helvetica, serif;
  }
}

// Code
pre {
  border: 2px solid var(--link-color);
  border-radius: 0.35rem;
  padding: 1rem;
}

code {
  background-color: var(--brand-background-lightest, #f9f9f9);
}

// Misc
small {
  font-size: 75%;
}

del {
  text-decoration-style: double;
  text-decoration-thickness: from-font;
}

hr {
  border: none;
  background: hsla(0, 0%, 0%, 0.2);
  height: 1px;
}

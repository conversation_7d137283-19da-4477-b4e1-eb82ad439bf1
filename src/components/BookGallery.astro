---
// Interactive 3D Book Gallery component
// Converted from the original Gatsby BookGallery component

interface Book {
  title: string
  yearOfPublication: number
  authors: string[]
  summary: string
  link: string
  coverUrl: string
  spineBackgroundColor?: string
  spineForegroundColor?: string
  size?: "lg" | "md" | "sm"
  fontSize?: string
}

interface Props {
  books: Book[]
}

const { books } = Astro.props
---

<div class="book-gallery">
  <!-- SVG filter for paper texture -->
  <svg class="paper-svg">
    <defs>
      <filter id="paper" x="0%" y="0%" width="100%" height="100%">
        <feTurbulence type="fractalNoise" baseFrequency="0.9" numOctaves="8" result="noise"></feTurbulence>
        <feDiffuseLighting in="noise" lighting-color="white" surfaceScale="1" result="diffLight">
          <feDistantLight azimuth="45" elevation="35"></feDistantLight>
        </feDiffuseLighting>
      </filter>
    </defs>
  </svg>

  <!-- Book list -->
  <div class="book-list" role="list">
    {
      books.map((book) => {
        const bookWidth = book.size === "lg" ? 62 : book.size === "md" ? 44 : 28
        return (
          <button
            class="book-cover"
            role="listitem"
            data-book={JSON.stringify(book)}
            style={`flex-basis: ${50 + bookWidth}px;`}
          >
            <div
              class="book-cover-side"
              style={`
              background-color: ${book.spineBackgroundColor ?? "#ccc"};
              color: ${book.spineForegroundColor ?? "#474545"};
              width: ${bookWidth}px;
            `}
            >
              <span aria-hidden="true" />
              <h2 style={`font-size: ${book.fontSize ?? "16px"};`}>{book.title}</h2>
            </div>
            <div class="book-cover-front">
              <span class="right" aria-hidden="true" />
              <span class="left" aria-hidden="true" />
              <img src={book.coverUrl} alt={book.title} />
            </div>
          </button>
        )
      })
    }
  </div>

  <!-- Selected book details -->
  <div class="selected-book" id="selected-book" style="display: none;">
    <dl>
      <dt>Title</dt>
      <dd class="book-title" style="font-size: 18px; font-style: italic;"></dd>
    </dl>
    <dl>
      <dt>Publication</dt>
      <dd class="book-year"></dd>
    </dl>
    <dl>
      <dt class="authors-label">Author</dt>
      <dd class="book-authors"></dd>
    </dl>
    <dl>
      <dt>What to expect?</dt>
      <dd class="book-summary"></dd>
    </dl>
    <p style="font-size: 18px;">
      Intrigued? <a class="book-link" href="#">Read my detailed notes</a>.
    </p>
  </div>
</div>

<script>
  document.addEventListener("astro:page-load", () => {
    const bookCovers = document.querySelectorAll(".book-cover")
    const selectedBookDiv = document.getElementById("selected-book")
    let currentSelectedBook = null

    bookCovers.forEach((bookCover) => {
      bookCover.addEventListener("click", () => {
        const bookData = JSON.parse(bookCover.dataset.book)
        const isCurrentlySelected = bookCover.classList.contains("is-selected")

        // Remove selection from all books
        bookCovers.forEach((cover) => {
          cover.classList.remove("is-selected")
          cover.style.flexBasis = `${50 + (cover.dataset.book ? (JSON.parse(cover.dataset.book).size === "lg" ? 62 : JSON.parse(cover.dataset.book).size === "md" ? 44 : 28) : 28)}px`
        })

        if (isCurrentlySelected) {
          // Deselect current book
          selectedBookDiv.style.display = "none"
          currentSelectedBook = null
        } else {
          // Select new book
          bookCover.classList.add("is-selected")
          bookCover.style.flexBasis = "240px"

          // Update selected book details
          selectedBookDiv.querySelector(".book-title").textContent = bookData.title
          selectedBookDiv.querySelector(".book-year").textContent = bookData.yearOfPublication
          selectedBookDiv.querySelector(".book-authors").textContent = bookData.authors.join(", ")
          selectedBookDiv.querySelector(".book-summary").textContent = bookData.summary
          selectedBookDiv.querySelector(".book-link").href = bookData.link
          selectedBookDiv.querySelector(".authors-label").textContent =
            bookData.authors.length > 1 ? "Authors" : "Author"

          selectedBookDiv.style.display = "block"
          currentSelectedBook = bookData
        }
      })
    })
  })
</script>

<style>
  .book-gallery {
    margin-top: 50px;
    margin-bottom: 60px;
  }

  .paper-svg {
    position: absolute;
    visibility: hidden;
    inset: 0;
  }

  .book-list {
    display: flex;
    flex-direction: row;
    justify-content: center;
    margin-right: 1rem;
    margin-left: 1rem;
  }

  .book-cover {
    display: flex;
    flex-shrink: 0;

    flex-basis: 100px;
    flex-direction: row;
    align-items: center;

    perspective: 1000px;
    -webkit-perspective: 1000px;
    appearance: button;
    -webkit-appearance: button;
    transition-duration: 500ms;

    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    will-change: auto;
    cursor: pointer;
    outline: 2px solid transparent;
    outline-offset: 2px;
    border: 0;
    background-image: none;
    background-color: transparent;
  }

  .book-cover:hover,
  .book-cover:focus-visible {
    transform: translateY(-16px);
  }

  .book-cover-side {
    flex-shrink: 0;

    transform: translate3d(0px, 0px, 0px) scale3d(1, 1, 1) rotateX(0deg) rotateY(-10deg) rotateZ(0deg) skew(0deg, 0deg);
    transform-origin: right;
    transform-style: preserve-3d;
    z-index: 50;
    filter: contrast(2) brightness(0.8);
    transition-duration: 500ms;

    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    will-change: auto;
    padding-top: 16px;
    padding-bottom: 16px;
    height: 288px;
  }

  .book-cover.is-selected .book-cover-side {
    transform: translate3d(0px, 0px, 0px) scale3d(1, 1, 1) rotateX(0deg) rotateY(-70deg) rotateZ(0deg) skew(0deg, 0deg);
  }

  .book-cover-side > span {
    position: fixed;
    top: 0;
    left: 0;
    opacity: 0.4;
    z-index: 50;
    filter: url(#paper);
    width: 100%;
    height: 100%;
    pointer-events: none;
  }

  .book-cover-side > h2 {
    margin: auto;
    font-weight: 500;
    writing-mode: vertical-lr;
  }

  .book-cover-front {
    position: relative;
    flex-shrink: 0;

    transform: translate3d(0px, 0px, 0px) scale3d(1, 1, 1) rotateX(0deg) rotateY(80deg) rotateZ(0deg) skew(0deg, 0deg);
    transform-origin: left;
    transform-style: preserve-3d;
    z-index: 10;
    filter: contrast(2) brightness(0.8);
    transition-duration: 500ms;

    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    will-change: auto;
    height: 288px;
    overflow: hidden;
  }

  .book-cover.is-selected .book-cover-front {
    transform: translate3d(0px, 0px, 0px) scale3d(1, 1, 1) rotateX(0deg) rotateY(20deg) rotateZ(0deg) skew(0deg, 0deg);
  }

  .book-cover-front > span {
    top: 0;
    z-index: 50;
    width: 100%;
    height: 100%;
    pointer-events: none;
  }

  .book-cover-front > span.right {
    position: fixed;
    right: 0;
    opacity: 0.4;
    filter: url(#paper);
  }

  .book-cover-front > span.left {
    position: absolute;
    left: 0;
    background: linear-gradient(
      to right,
      rgba(255, 255, 255, 0) 2px,
      rgba(255, 255, 255, 0.5) 3px,
      rgba(255, 255, 255, 0.25) 4px,
      rgba(255, 255, 255, 0.25) 6px,
      transparent 7px,
      transparent 9px,
      rgba(255, 255, 255, 0.25) 9px,
      transparent 12px
    );
  }

  .book-cover-front > img {
    transition-duration: 500ms;

    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    will-change: auto;
    background-size: cover;
    width: 192px;
    height: 100%;
  }

  .selected-book {
    margin-top: 40px;
  }

  .selected-book > dl {
    display: flex;
    margin-bottom: 0;
  }

  .selected-book > dl > dt {
    flex-shrink: 0;
    flex-basis: 160px;
    font-weight: 600;
  }
</style>

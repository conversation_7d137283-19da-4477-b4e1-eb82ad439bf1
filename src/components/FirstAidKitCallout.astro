---
const isFrench = window?.navigator.language.startsWith("fr")
---

{
  isFrench ? (
    <div class="callout">
      <em class="title-font">Trop de code Legacy à maintenir et pas assez de temps pour le nettoyer ?</em>
      <br />
      <span role="img" aria-label="Rescue Worker Helmet">
        ⛑️️
      </span>{" "}
      <strong>
        <a href="/premiers-soins">Mon kit Premiers Soins</a>
      </strong>{" "}
      peut secourir votre code <strong>rapidement</strong> et <strong>sereinement</strong> !
    </div>
  ) : (
    <div class="callout">
      <em class="title-font">Struggling with Legacy Code and not enough time to clean it up?</em>
      <br />
      <span role="img" aria-label="Rescue Worker Helmet">
        ⛑️️
      </span>{" "}
      <strong>
        <a href="/first-aid-kit">My First Aid Kit</a>
      </strong>{" "}
      can help you rescue any codebase <strong>quickly</strong> and <strong>safely</strong>!
    </div>
  )
}

<style lang="scss">
  .callout {
    margin: 3rem 0;
    border-left: 5px var(--first-aid-primary) solid;
    border-radius: 0 0.5rem 0.5rem 0;
    background: var(--first-aid-background);
    padding: 0.75rem 0 0.75rem 1rem;

    a {
      box-shadow: inset 0 -2px 0 var(--first-aid-primary);
    }
    a:hover,
    a:focus,
    a:active {
      box-shadow: inset 0 -1.3em 0 var(--first-aid-background);
    }
  }
</style>

---
// Size sm => ~200 pages / lg => 400+ pages
const books = [
  {
    title: "Working Effectively with Legacy Code",
    yearOfPublication: 2004,
    authors: ["<PERSON>"],
    summary:
      "This book is a reference. <PERSON><PERSON><PERSON> provides a wide range of strategies to help understand, refactor, and improve existing code. He emphasizes the importance of testing and shows various dependency-breaking techniques to facilitate the gradual transformation of legacy systems.",
    link: "/blog/key-points-of-working-effectively-with-legacy-code",
    coverUrl: "/assets/wewlc.jpeg",
    spineBackgroundColor: "#1e2020",
    spineForegroundColor: "#cccc68",
    size: "lg",
    fontSize: "14px",
  },
  {
    title: "Refactoring: Improving the Design of Existing Code",
    yearOfPublication: 2018,
    authors: ["<PERSON>"],
    summary:
      "The first edition came out in 1999! This book is a classic reference for any professional developer. In particular, it will teach you how to change the structure of existing code without breaking it. It's a catalog of moves you can lean on in your day-to-day work.",
    link: "/blog/key-points-of-refactoring",
    coverUrl: "/assets/refactoring.jpg",
    spineBackgroundColor: "#20191b",
    spineForegroundColor: "#fff",
    size: "md",
    fontSize: "10px",
  },
  {
    title: "Software Design X-Rays",
    yearOfPublication: 2018,
    authors: ["Adam Tornhill"],
    summary:
      "This book presents the concept of Behavioral Analysis. Adam Tornhill shows how to use git logs to infer insights about the codebase. For instance, he explains how to identify hotspots or find coupling between apparently unrelated files.",
    link: "/blog/key-points-of-software-design-x-rays",
    coverUrl: "/assets/software-design-xrays.jpg",
  },
  {
    title: "Legacy Code: First Aid Kit",
    yearOfPublication: 2021,
    authors: ["Nicolas Carlo (hey, itsa me! ⭐)"],
    summary:
      "I wrote this one. I detailed the techniques I use the most to tame legacy codebases. This is a book with concrete code examples, written from the trenches.",
    link: "/first-aid-kit",
    coverUrl: "/assets/first-aid-kit-cover.png",
    spineBackgroundColor: "#ed1b2e",
    spineForegroundColor: "#fff",
  },
  {
    title: "The Programmer's Brain",
    yearOfPublication: 2021,
    authors: ["Dr. Felienne Hermans"],
    summary:
      "You will find here a mix of cognitive science and programming. Dr. Felienne Hermans explains how to best approach unfamiliar codebases, based on how your brain works.",
    link: "/blog/key-points-of-programmer-brain",
    coverUrl: "/assets/the-programmer-brain.png",
    spineBackgroundColor: "#1c293d",
    spineForegroundColor: "#d2aa4a",
  },
  {
    title: "Refactoring at Scale",
    yearOfPublication: 2020,
    authors: ["Maude Lemaire"],
    summary:
      "Maude Lemaire wrote a great book rooted from her experience in the trenches, at Slack. Let's see how it may help you lead large-scale refactoring projects.",
    link: "/blog/key-points-of-refactoring-at-scale",
    coverUrl: "/assets/refactoring-at-scale.jpg",
  },
  {
    title: "Kill It with Fire",
    yearOfPublication: 2021,
    authors: ["Marianne Belotti"],
    summary:
      "This is a great book for anyone involved modernization projects, and for tech leaders in particular. Marianne Belotti shares lessons she learned from personal experience.",
    link: "/blog/key-points-of-kill-it-with-fire",
    coverUrl: "/assets/kill-it-with-fire.jpg",
    spineBackgroundColor: "#244565",
    spineForegroundColor: "#db664b",
    size: "md",
  },
  {
    title: "Beyond Legacy Code",
    yearOfPublication: 2015,
    authors: ["David Bernstein"],
    summary:
      "David Bernstein shares nine concepts and strategies to help you manage legacy code, from automating your tests suite to setting up a Buddy program.",
    link: "/blog/key-points-of-beyond-legacy-code",
    coverUrl: "/assets/beyond-legacy-code.jpg",
  },
  {
    title: "The Legacy Code Programmer's Toolbox",
    yearOfPublication: 2019,
    authors: ["Jonathan Boccara"],
    summary:
      "In this book, Jonathan Boccara shares a collection of techniques he recommends using on legacy codebases. From chosing a stronghold to setting up Dailies at work, it might inspire you.",
    link: "/blog/key-points-of-legacy-code-programmer-toolbox",
    coverUrl: "/assets/legacy-code-progammer-toolbox.jpg",
    fontSize: "12px",
  },
  {
    title: "Re-Engineering Legacy Software",
    yearOfPublication: 2016,
    authors: ["Chris Birchall"],
    summary:
      "I particularly like the fact that Chris Birchall not only shares techniques to improve Legacy Code, but also how to address the Legacy Culture.",
    link: "/blog/key-points-reengineering-legacy-software",
    coverUrl: "/assets/re-engineering-legacy-software.png",
    spineBackgroundColor: "#17191e",
    spineForegroundColor: "#d73639",
    fontSize: "14px",
  },
]
---

<div class="space-y-4">
  <p>Few books dig into the challenges of working with legacy systems. These ones do.</p>
  <p>
    Here is my personal bookshelf. For each book, I've written a high-level overview of what you can expect from it.
    Their salient points, according to me.
  </p>
  <p>Click the ones that intrigue you to get more details. I put the ones that were the most insightful to me first.</p>
</div>

<div class="books-grid">
  {
    books.map((book) => (
      <div class="book-item">
        <a href={book.link} class="book-link">
          <img src={book.coverUrl} alt={`Cover of ${book.title}`} class="book-cover" />
        </a>
        <div class="book-info">
          <a href={book.link} class="book-title title-font">
            {book.title}
          </a>
          <p class="book-authors">
            by {book.authors.join(", ")} ({book.yearOfPublication})
          </p>
          <p class="book-summary">{book.summary}</p>
        </div>
      </div>
    ))
  }
</div>

<!-- Interactive Book Gallery -->
<div class="books-gallery" style="margin-top: 50px; margin-bottom: 60px;">
  <svg class="paper">
    <defs>
      <filter id="paper" x="0%" y="0%" width="100%" height="100%">
        <feTurbulence type="fractalNoise" baseFrequency="0.9" numOctaves="8" result="noise"></feTurbulence>
        <feDiffuseLighting in="noise" lighting-color="white" surfaceScale="1" result="diffLight">
          <feDistantLight azimuth="45" elevation="35"></feDistantLight>
        </feDiffuseLighting>
      </filter>
    </defs>
  </svg>

  <!-- Inspired from https://alexandru.so/experiments/book-gallery -->
  <div class="book-list" role="list">
    {
      books.map((book) => {
        const bookWidth = book.size === "lg" ? 62 : book.size === "md" ? 44 : 28

        return (
          <button
            class="book-cover"
            role="listitem"
            data-book-title={book.title}
            data-book-width={bookWidth}
            style={`flex-basis: ${50 + bookWidth}px;`}
          >
            <div
              class="book-cover-side"
              style={`
                background-color: ${book.spineBackgroundColor ?? "#ccc"};
                color: ${book.spineForegroundColor ?? "#474545"};
                width: ${bookWidth}px;
              `}
            >
              <span aria-hidden="true" />
              <h2 style={`font-size: ${book.fontSize ?? "16px"};`}>{book.title}</h2>
            </div>
            <div class="book-cover-front">
              <span class="right" aria-hidden="true" />
              <span class="left" aria-hidden="true" />
              <img src={book.coverUrl} alt={book.title} />
            </div>
          </button>
        )
      })
    }
  </div>

  <div class="selected" id="selected-book" style="display: none;">
    <dl>
      <dt>Title</dt>
      <dd id="selected-title" style="font-size: 18px; font-style: italic;"></dd>
    </dl>
    <dl>
      <dt>Publication</dt>
      <dd id="selected-year"></dd>
    </dl>
    <dl>
      <dt id="authors-label">Author</dt>
      <dd id="selected-authors"></dd>
    </dl>
    <dl>
      <dt>What to expect?</dt>
      <dd id="selected-summary"></dd>
    </dl>

    <p style="font-size: 18px;">
      Intrigued? <a id="selected-link" href="#">Read my detailed notes</a>.
    </p>
  </div>
</div>

<script define:vars={{ books }} is:inline>
  let selectedBook = null

  function selectBook(book) {
    const selectedDiv = document.getElementById("selected-book")
    const titleEl = document.getElementById("selected-title")
    const yearEl = document.getElementById("selected-year")
    const authorsEl = document.getElementById("selected-authors")
    const authorsLabelEl = document.getElementById("authors-label")
    const summaryEl = document.getElementById("selected-summary")
    const linkEl = document.getElementById("selected-link")

    if (selectedBook === book) {
      // Deselect if clicking the same book
      selectedBook = null
      if (selectedDiv) selectedDiv.style.display = "none"

      // Remove is-selected class from all buttons
      document.querySelectorAll(".book-cover").forEach((btn) => {
        const htmlBtn = btn
        htmlBtn.classList.remove("is-selected")
        const bookWidth = parseInt(htmlBtn.getAttribute("data-book-width") || "28")
        htmlBtn.style.setProperty("flex-basis", `${50 + bookWidth}px`)
      })
    } else {
      // Select new book
      selectedBook = book
      if (selectedDiv) selectedDiv.style.display = "block"

      if (titleEl) titleEl.textContent = book.title
      if (yearEl) yearEl.textContent = book.yearOfPublication.toString()
      if (authorsEl) authorsEl.textContent = book.authors.join(", ")
      if (authorsLabelEl) authorsLabelEl.textContent = book.authors.length > 1 ? "Authors" : "Author"
      if (summaryEl) summaryEl.textContent = book.summary
      if (linkEl && linkEl instanceof HTMLAnchorElement) linkEl.href = book.link

      // Update button states
      document.querySelectorAll(".book-cover").forEach((btn) => {
        const htmlBtn = btn
        const isCurrentBook = htmlBtn.getAttribute("data-book-title") === book.title
        const bookWidth = parseInt(htmlBtn.getAttribute("data-book-width") || "28")

        if (isCurrentBook) {
          htmlBtn.classList.add("is-selected")
          htmlBtn.style.setProperty("flex-basis", "240px")
        } else {
          htmlBtn.classList.remove("is-selected")
          htmlBtn.style.setProperty("flex-basis", `${50 + bookWidth}px`)
        }
      })
    }
  }

  function cleanup() {
    selectedBook = null
    const selectedDiv = document.getElementById("selected-book")
    if (selectedDiv) selectedDiv.style.display = "none"

    document.querySelectorAll(".book-cover").forEach((btn) => {
      btn.classList.remove("is-selected")
      const bookWidth = parseInt(btn.getAttribute("data-book-width") || "28")
      btn.style.setProperty("flex-basis", `${50 + bookWidth}px`)
    })
  }

  // Clean up when navigating away
  document.addEventListener("astro:before-preparation", cleanup)

  // Add click event listeners to all book buttons
  document.addEventListener("astro:page-load", () => {
    document.querySelectorAll(".book-cover").forEach((button) => {
      button.addEventListener("click", () => {
        const bookTitle = button.getAttribute("data-book-title")
        const matchingBook = books.find((b) => b.title === bookTitle)
        if (matchingBook) {
          selectBook(matchingBook)
        }
      })
    })
  })
</script>

<style>
  .books-grid {
    display: none;
  }

  svg.paper {
    position: absolute;
    visibility: hidden;
    inset: 0;
  }

  .book-list {
    display: flex;
    flex-direction: row;
    justify-content: center;
    margin-right: 1rem;
    margin-left: 1rem;
  }

  .selected {
    margin-top: 40px;

    & > dl {
      display: flex;
      margin-bottom: 0;

      & > dt {
        flex-shrink: 0;
        flex-basis: 160px;
        font-weight: 600;
      }

      & dd {
        margin-bottom: 1.75rem;
      }
    }
  }

  .book-cover {
    display: flex;
    flex-shrink: 0;

    flex-basis: 100px;
    flex-direction: row;
    align-items: center;

    perspective: 1000px;
    -webkit-perspective: 1000px;
    appearance: button;
    -webkit-appearance: button;
    transition-duration: 500ms;

    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    will-change: auto;
    cursor: pointer;
    outline: 2px solid transparent !important;
    outline-offset: 2px;
    border: 0;
    background-image: none;
    background-color: transparent;
    &:hover,
    &:focus-visible {
      transform: translateY(-16px);
    }
  }

  .book-cover-side {
    flex-shrink: 0;

    transform: translate3d(0px, 0px, 0px) scale3d(1, 1, 1) rotateX(0deg) rotateY(-10deg) rotateZ(0deg) skew(0deg, 0deg);
    transform-origin: right;
    transform-style: preserve-3d;
    z-index: 50;
    filter: contrast(2) brightness(0.8);
    transition-duration: 500ms;

    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    will-change: auto;
    padding-top: 16px;
    padding-bottom: 16px;
    height: 288px;
    .is-selected & {
      transform: translate3d(0px, 0px, 0px) scale3d(1, 1, 1) rotateX(0deg) rotateY(-70deg) rotateZ(0deg)
        skew(0deg, 0deg);
    }

    & > span {
      position: fixed;
      top: 0;
      left: 0;
      opacity: 0.4;
      z-index: 50;
      filter: url(#paper);
      width: 100%;
      height: 100%;
      pointer-events: none;
    }

    & > h2 {
      margin: auto;
      font-weight: 500;
      line-height: 1.1;
      writing-mode: vertical-lr;
    }
  }

  .book-cover-front {
    position: relative;
    flex-shrink: 0;

    transform: translate3d(0px, 0px, 0px) scale3d(1, 1, 1) rotateX(0deg) rotateY(80deg) rotateZ(0deg) skew(0deg, 0deg);
    transform-origin: left;
    transform-style: preserve-3d;
    z-index: 10;
    filter: contrast(2) brightness(0.8);
    transition-duration: 500ms;

    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    will-change: auto;
    height: 288px;
    overflow: hidden;
    .is-selected & {
      transform: translate3d(0px, 0px, 0px) scale3d(1, 1, 1) rotateX(0deg) rotateY(20deg) rotateZ(0deg) skew(0deg, 0deg);
    }

    & > span {
      top: 0;
      z-index: 50;
      width: 100%;
      height: 100%;
      pointer-events: none;
    }

    & > span.right {
      position: fixed;
      right: 0;
      opacity: 0.4;
      filter: url(#paper);
    }

    & > span.left {
      position: absolute;
      left: 0;
      background: linear-gradient(
        to right,
        rgba(255, 255, 255, 0) 2px,
        rgba(255, 255, 255, 0.5) 3px,
        rgba(255, 255, 255, 0.25) 4px,
        rgba(255, 255, 255, 0.25) 6px,
        transparent 7px,
        transparent 9px,
        rgba(255, 255, 255, 0.25) 9px,
        transparent 12px
      );
    }

    & > img {
      transition-duration: 500ms;

      transition-property: all;
      transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
      will-change: auto;
      background-size: cover;
      width: 192px;
      height: 100%;
    }
  }

  @media (max-width: 768px) {
    .book-item {
      flex-direction: column;
      text-align: center;
    }

    .book-cover {
      margin: 0 auto;
      width: 100px;
    }
  }

  @media (max-width: 1536px) {
    .books-gallery {
      display: none;
    }

    .books-grid {
      display: grid;
      grid-template-columns: 1fr;
      gap: 2rem;
      margin: 2rem 0;
    }

    .book-item {
      display: flex;
      gap: 1rem;
      border-radius: 0.5rem;
      background: var(--brand-background-lightest, #f9f9f9);
      padding: 1rem;
    }

    a.book-link {
      box-shadow: none;
    }

    .book-cover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      border-radius: 0.25rem;
      width: 120px;
      height: auto;
    }

    .book-info {
      flex: 1;
    }

    .book-title {
      margin-bottom: 0.5rem;
      font-weight: bold;
      font-size: 1.2rem;
    }

    .book-authors {
      margin-bottom: 0.75rem;
      color: #666;
      font-size: 0.9rem;
    }

    .book-summary {
      margin-bottom: 0;
      line-height: 1.5;
    }
  }
</style>
